{"kind": "collectionType", "collectionName": "countries", "info": {"singularName": "country", "pluralName": "countries", "displayName": "Countries", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "name_ar": {"type": "string"}, "cities": {"type": "relation", "relation": "oneToMany", "target": "api::city.city", "mappedBy": "country"}, "iso_code": {"type": "string"}, "config": {"type": "relation", "relation": "manyToOne", "target": "api::config.config", "inversedBy": "countries"}, "icon": {"type": "media", "multiple": false, "allowedTypes": ["images", "files", "videos", "audios"]}}}