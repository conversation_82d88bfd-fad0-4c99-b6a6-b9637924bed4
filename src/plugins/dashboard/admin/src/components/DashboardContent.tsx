import { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Flex,
  IconButton
} from '@strapi/design-system';
import { ArrowLeft, Plus } from '@strapi/icons';
import CollectionManager from './CollectionManager';

interface DashboardContentProps {
  collection: string;
  onBack: () => void;
}

const collectionConfig = {
  banners: {
    name: 'Banners',
    singular: 'Banner',
    apiEndpoint: '/banners',
    fields: [
      // Title is not required per schema.json (no required: true on title)
      { key: 'title', label: 'Title', type: 'text', required: false },
      { key: 'media', label: 'Media', type: 'media', required: false },
      { key: 'vendor', label: 'Vendor', type: 'relation', target: 'vendors', required: false }
    ]
  },
  products: {
    name: 'Products',
    singular: 'Product',
    apiEndpoint: '/products',
    fields: [
      { key: 'title', label: 'Title', type: 'text', required: true },
      { key: 'title_ar', label: 'Title (Arabic)', type: 'text', required: false },
      { key: 'description', label: 'Description', type: 'textarea', required: false },
      { key: 'price', label: 'Price', type: 'number', required: true },
      { key: 'images', label: 'Images', type: 'media', multiple: true, required: false }
    ]
  },
  vendors: {
    name: 'Vendors',
    singular: 'Vendor',
    apiEndpoint: '/vendors',
    fields: [
      { key: 'name', label: 'Name', type: 'text', required: true },
      { key: 'business_name', label: 'Business Name', type: 'text', required: true },
      { key: 'email', label: 'Email', type: 'email', required: true },
      { key: 'phone', label: 'Phone', type: 'text', required: false }
    ]
  }
};

const DashboardContent = ({ collection, onBack }: DashboardContentProps) => {
  const [mode, setMode] = useState<'list' | 'create' | 'edit'>('list');
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const config = collectionConfig[collection as keyof typeof collectionConfig];

  if (!config) {
    return (
      <Box padding={8}>
        <Flex alignItems="center" gap={2} marginBottom={4}>
          <IconButton onClick={onBack} label="Go back">
            <ArrowLeft />
          </IconButton>
          <Typography variant="alpha">Collection not found</Typography>
        </Flex>
      </Box>
    );
  }

  const handleCreate = () => {
    setSelectedItem(null);
    setMode('create');
  };

  const handleEdit = (item: any) => {
    setSelectedItem(item);
    setMode('edit');
  };

  const handleBackToList = () => {
    setMode('list');
    setSelectedItem(null);
  };

  return (
    <Box padding={8}>
      {/* Header */}
      <Flex alignItems="center" justifyContent="space-between" marginBottom={6}>
        <Flex alignItems="center" gap={2}>
          <IconButton onClick={onBack} label="Go back">
            <ArrowLeft />
          </IconButton>
          <Typography variant="alpha">
            {mode === 'create' ? `Create ${config.singular}` :
             mode === 'edit' ? `Edit ${config.singular}` :
             config.name}
          </Typography>
        </Flex>

        {mode === 'list' && (
          <Button startIcon={<Plus />} onClick={handleCreate}>
            Add {config.singular}
          </Button>
        )}
      </Flex>

      {/* Content */}
      <CollectionManager
        config={config}
        mode={mode}
        selectedItem={selectedItem}
        onEdit={handleEdit}
        onBackToList={handleBackToList}
      />
    </Box>
  );
};

export default DashboardContent;
