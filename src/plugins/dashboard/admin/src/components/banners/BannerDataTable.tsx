import { useState, useEffect, useRef } from 'react';
import { getTranslation } from '../../utils/getTranslation';
import { useSafeIntl } from '../../utils/contextSafety';
import VendorDropdown from '../ui/shared/VendorDropdown';
import StatusBadge from '../ui/shared/StatusBadge';
import ActionButtons from '../ui/shared/ActionButtons';
import Pagination from '../ui/shared/Pagination';
import DeleteConfirmationModal from '../ui/modals/DeleteConfirmationModal';
import AddBannerModal from './AddBannerModal';
import EditBannerModal from './EditBannerModal';
import { bannerService, Banner, BannerCreateData, BannerUpdateData } from '../../services/bannerService';

interface AdvancedFilters {
  dateFrom: string;
  dateTo: string;
  status: string;
}

interface BannerDataTableProps {
  searchTerm: string;
  vendorFilter: string;
  advancedFilters: AdvancedFilters;
}

const BannerDataTable = ({ searchTerm, vendorFilter, advancedFilters }: BannerDataTableProps) => {
  const { formatMessage } = useSafeIntl();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    direction: 'asc' | 'desc' | null;
  }>({
    key: null,
    direction: null
  });
  const [showFieldsSettings, setShowFieldsSettings] = useState(false);
  const [visibleFields, setVisibleFields] = useState({
    id: true,
    documentId: false,
    image: true,
    title: true,
    vendor: true,
    status: false,
    createdAt: true,
    actions: true
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);

  // API state
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    itemId: string | null;
    itemName: string;
    isBulk: boolean;
  }>({
    isOpen: false,
    itemId: null,
    itemName: '',
    isBulk: false
  });

  // Ref for field settings popup
  const fieldSettingsRef = useRef<HTMLDivElement>(null);

  // Close field settings when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fieldSettingsRef.current && !fieldSettingsRef.current.contains(event.target as Node)) {
        setShowFieldsSettings(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Load banners from API
  const loadBanners = async () => {
    try {
      setLoading(true);
      setError(null);

      const sortParam = sortConfig.key && sortConfig.direction
        ? `${sortConfig.key}:${sortConfig.direction}`
        : undefined;

      const response = await bannerService.findMany({
        page: currentPage,
        pageSize: itemsPerPage,
        search: searchTerm,
        vendor: vendorFilter,
        status: advancedFilters.status || undefined,
        dateFrom: advancedFilters.dateFrom,
        dateTo: advancedFilters.dateTo,
        sort: sortParam
      });

      setBanners(response.data);
      setTotalItems(response.meta.pagination.total);
      setTotalPages(response.meta.pagination.pageCount);
    } catch (err) {
      console.error('Error loading banners:', err);
      setError('Failed to load banners');
    } finally {
      setLoading(false);
    }
  };

  // Load banners when dependencies change
  useEffect(() => {
    loadBanners();
  }, [currentPage, itemsPerPage, searchTerm, vendorFilter, advancedFilters, sortConfig]);

  // CRUD operations
  const handleAddBanner = async (data: BannerCreateData) => {
    try {
      await bannerService.create(data);
      await loadBanners(); // Reload the list
      setShowAddModal(false);
    } catch (error) {
      console.error('Error creating banner:', error);
      setError('Failed to create banner');
    }
  };

  const handleEditBanner = async (data: BannerUpdateData) => {
    if (!editingBanner) return;

    try {
      await bannerService.update(editingBanner.documentId, data);
      await loadBanners(); // Reload the list
      setShowEditModal(false);
      setEditingBanner(null);
    } catch (error) {
      console.error('Error updating banner:', error);
      setError('Failed to update banner');
    }
  };

  const handleDeleteBanner = async (documentId: string) => {
    try {
      await bannerService.delete(documentId);
      await loadBanners(); // Reload the list
      setSelectedItems(prev => prev.filter(id => id !== documentId));
    } catch (error) {
      console.error('Error deleting banner:', error);
      setError('Failed to delete banner');
    }
  };

  const handleBulkDeleteBanners = async (documentIds: string[]) => {
    try {
      await bannerService.bulkDelete(documentIds);
      await loadBanners(); // Reload the list
      setSelectedItems([]);
    } catch (error) {
      console.error('Error bulk deleting banners:', error);
      setError('Failed to delete banners');
    }
  };

  // Handle sorting
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' | null = 'desc'; // First click sorts by descending

    if (sortConfig.key === key) {
      if (sortConfig.direction === 'desc') {
        direction = 'asc';
      } else if (sortConfig.direction === 'asc') {
        direction = null;
      }
    }

    setSortConfig({ key: direction ? key : null, direction });
  };

  // Handle field visibility toggle
  const toggleFieldVisibility = (field: keyof typeof visibleFields) => {
    // Don't allow hiding actions column
    if (field === 'actions') return;

    setVisibleFields({
      ...visibleFields,
      [field]: !visibleFields[field]
    });
  };

  // Current banners for display (already filtered and paginated by API)
  const currentBanners = banners;

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(currentBanners.map(banner => banner.documentId));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (documentId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, documentId]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== documentId));
    }
  };

  const handleView = (documentId: string) => {
    if (expandedRow === documentId) {
      setExpandedRow(null);
    } else {
      setExpandedRow(documentId);
    }
  };

  const handleEdit = (documentId: string) => {
    const banner = banners.find(b => b.documentId === documentId);
    if (banner) {
      setEditingBanner(banner);
      setShowEditModal(true);
    }
  };

  const handleDelete = (documentId: string) => {
    const banner = banners.find(b => b.documentId === documentId);
    setDeleteModal({
      isOpen: true,
      itemId: documentId,
      itemName: banner?.title || 'Unknown Banner',
      isBulk: false
    });
  };

  const handleBulkDelete = () => {
    setDeleteModal({
      isOpen: true,
      itemId: null,
      itemName: `${selectedItems.length} banners`,
      isBulk: true
    });
  };

  const confirmDelete = async () => {
    try {
      if (deleteModal.isBulk) {
        await handleBulkDeleteBanners(selectedItems);
      } else if (deleteModal.itemId) {
        await handleDeleteBanner(deleteModal.itemId);
      }
      setDeleteModal({ isOpen: false, itemId: null, itemName: '', isBulk: false });
    } catch (error) {
      console.error('Error in confirmDelete:', error);
    }
  };

  // Function to get grid template columns based on visible fields
  const getGridTemplateColumns = () => {
    let columns = [];

    // Always include checkbox column (50px for better spacing)
    columns.push('50px');

    // Fixed width columns with better spacing
    if (visibleFields.documentId) columns.push('140px');
    if (visibleFields.image) columns.push('100px');

    // Flexible columns - title gets more space
    if (visibleFields.title) columns.push('minmax(200px, 2fr)');
    if (visibleFields.vendor) columns.push('minmax(150px, 1fr)');
    if (visibleFields.status) columns.push('120px');
    if (visibleFields.createdAt) columns.push('140px');

    // Actions column is always fixed at the end with adequate space
    columns.push('120px');

    return columns.join(' ');
  };

  // Show loading state
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '40px',
        color: '#a5a5ba'
      }}>
        <div style={{
          width: '32px',
          height: '32px',
          border: '3px solid #32324d',
          borderTop: '3px solid #4945ff',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <span style={{ marginLeft: '12px' }}>Loading banners...</span>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div style={{
        padding: '40px',
        textAlign: 'center',
        color: '#ff5252',
        backgroundColor: '#181826',
        borderRadius: '4px',
        border: '1px solid #32324d'
      }}>
        <p>{error}</p>
        <button
          onClick={loadBanners}
          style={{
            padding: '8px 16px',
            backgroundColor: '#4945ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '12px'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div>
      {/* Table Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: 'white',
            margin: 0
          }}>
            Banners ({totalItems})
          </h3>

          {selectedItems.length > 0 && (
            <button
              onClick={handleBulkDelete}
              style={{
                padding: '8px 16px',
                backgroundColor: '#d32f2f',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              Delete Selected ({selectedItems.length})
            </button>
          )}
        </div>

        {/* Action Buttons */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {/* Field Settings Button */}
          <div style={{ position: 'relative' }} ref={fieldSettingsRef}>
            <button
              onClick={() => setShowFieldsSettings(!showFieldsSettings)}
              title="Display settings"
              style={{
                width: '32px',
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: showFieldsSettings ? '#32324d' : 'transparent',
                border: '1px solid #32324d',
                borderRadius: '4px',
                color: showFieldsSettings ? 'white' : '#a5a5ba',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                if (!showFieldsSettings) {
                  e.currentTarget.style.backgroundColor = '#32324d';
                  e.currentTarget.style.color = 'white';
                }
              }}
              onMouseLeave={(e) => {
                if (!showFieldsSettings) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = '#a5a5ba';
                }
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke="currentColor" strokeWidth="2"/>
                <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </button>

            {showFieldsSettings && (
              <div style={{
                position: 'absolute',
                top: '100%',
                right: 0,
                marginTop: '8px',
                backgroundColor: '#212134',
                border: '1px solid #32324d',
                borderRadius: '4px',
                padding: '12px',
                zIndex: 1000,
                width: '200px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
              }}>
                <div style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '8px'
                }}>
                  Display Fields
                </div>

                {Object.entries(visibleFields)
                  .filter(([field]) => field !== 'actions') // Exclude actions from settings
                  .map(([field, isVisible]) => (
                  <label
                    key={field}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '6px 0',
                      cursor: 'pointer',
                      color: 'white'
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={isVisible}
                      onChange={() => toggleFieldVisibility(field as keyof typeof visibleFields)}
                      style={{
                        margin: 0,
                        width: '14px',
                        height: '14px',
                        appearance: 'none',
                        WebkitAppearance: 'none',
                        MozAppearance: 'none',
                        backgroundColor: 'transparent',
                        border: '1px solid #32324d',
                        borderRadius: '3px',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        (e.currentTarget as HTMLInputElement).style.borderColor = '#4945ff';
                      }}
                      onMouseLeave={(e) => {
                        (e.currentTarget as HTMLInputElement).style.borderColor = '#32324d';
                      }}
                    />
                    <span style={{ fontSize: '14px' }}>
                      {field === 'documentId' ? 'Document ID' : field.charAt(0).toUpperCase() + field.slice(1)}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Add Banner Button */}
          <button
            onClick={() => setShowAddModal(true)}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4945ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            + Add Banner
          </button>
        </div>
      </div>



      {/* Table */}
      <div style={{
        backgroundColor: '#181826',
        borderRadius: '4px',
        border: '1px solid #32324d',
        overflow: 'auto',
        maxWidth: '100%'
      }}>
        {/* Table Header */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: getGridTemplateColumns(),
          gap: '16px',
          padding: '12px 16px',
          backgroundColor: '#32324d',
          borderBottom: '1px solid #32324d',
          fontSize: '12px',
          fontWeight: 'bold',
          color: '#a5a5ba',
          textTransform: 'uppercase',
          minWidth: '900px' // Ensure minimum width for proper spacing
        }}>
          {/* Checkbox column is always visible */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              type="checkbox"
              checked={selectedItems.length === currentBanners.length && currentBanners.length > 0}
              onChange={(e) => handleSelectAll(e.target.checked)}
              style={{
                margin: 0,
                width: '16px',
                height: '16px',
                appearance: 'none',
                WebkitAppearance: 'none',
                MozAppearance: 'none',
                backgroundColor: 'transparent',
                border: '1px solid #32324d',
                borderRadius: '3px',
                position: 'relative'
              }}
              onMouseEnter={(e) => {
                (e.currentTarget as HTMLInputElement).style.borderColor = '#4945ff';
              }}
              onMouseLeave={(e) => {
                (e.currentTarget as HTMLInputElement).style.borderColor = '#32324d';
              }}
            />
              style={{ margin: 0 }}
            />
            {visibleFields.id && (
              <span
                onClick={() => handleSort('id')}
                style={{
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                ID
                {sortConfig.key === 'id' && (
                  <span>
                    {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                  </span>
                )}
              </span>
            )}
          </div>

          {visibleFields.documentId && (
            <div>Document ID</div>
          )}

          {visibleFields.image && (
            <div>Image</div>
          )}

          {visibleFields.title && (
            <div
              onClick={() => handleSort('title')}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              Title
              {sortConfig.key === 'title' && (
                <span>
                  {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                </span>
              )}
            </div>
          )}

          {visibleFields.vendor && (
            <div
              onClick={() => handleSort('vendor')}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              Vendor
              {sortConfig.key === 'vendor' && (
                <span>
                  {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                </span>
              )}
            </div>
          )}

          {visibleFields.status && (
            <div
              onClick={() => handleSort('status')}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              Status
              {sortConfig.key === 'status' && (
                <span>
                  {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                </span>
              )}
            </div>
          )}

          {visibleFields.createdAt && (
            <div
              onClick={() => handleSort('createdAt')}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              Created
              {sortConfig.key === 'createdAt' && (
                <span>
                  {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                </span>
              )}
            </div>
          )}

          {/* Actions column is always visible */}
          <div>Actions</div>
        </div>

        {/* Table Body */}
        {currentBanners.length > 0 ? (
          currentBanners.map((banner) => (
            <div key={banner.documentId}>
              {/* Main Row */}
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: getGridTemplateColumns(),
                  gap: '16px',
                  padding: '12px 16px',
                  borderBottom: expandedRow === banner.documentId ? 'none' : '1px solid #32324d',
                  alignItems: 'center',
                  fontSize: '14px',
                  color: 'white',
                  backgroundColor: expandedRow === banner.documentId ? '#32324d' : 'transparent',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s',
                  position: 'relative',
                  minWidth: '900px' // Match header minimum width
                }}
                onClick={() => handleView(banner.documentId)}
                onMouseEnter={(e) => {
                  if (expandedRow !== banner.documentId) {
                    e.currentTarget.style.backgroundColor = '#1e1e30';
                  }
                }}
                onMouseLeave={(e) => {
                  if (expandedRow !== banner.documentId) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                  setHoveredImage(null);
                }}
              >
                {/* Checkbox column is always visible */}
                <div onClick={(e) => e.stopPropagation()} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(banner.documentId)}
                    onChange={(e) => handleSelectItem(banner.documentId, e.target.checked)}
                    style={{
                      margin: 0,
                      width: '16px',
                      height: '16px',
                      appearance: 'none',
                      WebkitAppearance: 'none',
                      MozAppearance: 'none',
                      backgroundColor: 'transparent',
                      border: '1px solid #32324d',
                      borderRadius: '3px',
                      position: 'relative'
                    }}
                    onMouseEnter={(e) => {
                      (e.currentTarget as HTMLInputElement).style.borderColor = '#4945ff';
                    }}
                    onMouseLeave={(e) => {
                      (e.currentTarget as HTMLInputElement).style.borderColor = '#32324d';
                    }}
                  />
                  {visibleFields.id && (
                    <span style={{ color: '#a5a5ba', fontSize: '12px', fontFamily: 'monospace' }}>
                      {banner.id}
                    </span>
                  )}
                </div>

                {visibleFields.documentId && (
                  <span style={{ color: '#a5a5ba', fontSize: '12px', fontFamily: 'monospace' }}>
                    {banner.documentId}
                  </span>
                )}

                {visibleFields.image && (
                  <div
                    style={{
                      width: '60px',
                      height: '40px',
                      backgroundColor: '#32324d',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '20px',
                      overflow: 'hidden',
                      position: 'relative'
                    }}
                    onMouseEnter={() => banner.media?.url && setHoveredImage(banner.documentId)}
                  >
                    {banner.media?.url ? (
                      <img
                        src={banner.media.url}
                        alt={banner.title}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                    ) : (
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="#a5a5ba" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z" stroke="#a5a5ba" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M21 15L16 10L5 21" stroke="#a5a5ba" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}

                    {/* Image Preview on Hover */}
                    {hoveredImage === banner.documentId && banner.media?.url && (
                      <div style={{
                        position: 'absolute',
                        top: '-20px',
                        left: '70px',
                        zIndex: 1000,
                        backgroundColor: '#212134',
                        padding: '4px',
                        borderRadius: '4px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
                        border: '1px solid #32324d'
                      }}>
                        <img
                          src={banner.media.url}
                          alt={banner.title}
                          style={{
                            maxWidth: '300px',
                            maxHeight: '200px',
                            objectFit: 'contain'
                          }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {visibleFields.title && (
                  <span style={{ fontWeight: '500' }}>{banner.title}</span>
                )}

                {visibleFields.vendor && (
                  <span style={{ color: '#a5a5ba' }}>{banner.vendor?.name || 'No vendor'}</span>
                )}

                {visibleFields.status && (
                  <StatusBadge status={banner.publishedAt ? 'published' : 'draft'} />
                )}

                {visibleFields.createdAt && (
                  <span style={{ color: '#a5a5ba', fontSize: '12px' }}>
                    {new Date(banner.createdAt).toLocaleDateString()}
                  </span>
                )}

                {/* Actions column is always visible */}
                <div onClick={(e) => e.stopPropagation()}>
                  <ActionButtons
                    onView={() => handleView(banner.documentId)}
                    onEdit={() => handleEdit(banner.documentId)}
                    onDelete={() => handleDelete(banner.documentId)}
                    variant="icon"
                    isExpanded={expandedRow === banner.documentId}
                  />
                </div>
              </div>

              {/* Expanded Details */}
              {expandedRow === banner.documentId && (
                <div style={{
                  padding: '20px',
                  backgroundColor: '#181826',
                  borderBottom: '1px solid #32324d',
                  borderLeft: '3px solid #4945ff'
                }}>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '24px'
                  }}>
                    {/* Left Column */}
                    <div>
                      <h4 style={{
                        fontSize: '16px',
                        fontWeight: 'bold',
                        color: 'white',
                        marginBottom: '12px',
                        margin: '0 0 12px 0'
                      }}>
                        Banner Details
                      </h4>

                      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>ID:</span>
                          <span style={{ color: 'white', marginLeft: '8px' }}>{banner.id}</span>
                        </div>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Document ID:</span>
                          <span style={{ color: 'white', marginLeft: '8px', fontFamily: 'monospace' }}>{banner.documentId}</span>
                        </div>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Title:</span>
                          <span style={{ color: 'white', marginLeft: '8px' }}>{banner.title}</span>
                        </div>

                        {banner.media?.url && (
                          <div>
                            <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Image:</span>
                            <div style={{ marginTop: '8px' }}>
                              <img
                                src={banner.media.url}
                                alt={banner.title}
                                style={{
                                  maxWidth: '100%',
                                  maxHeight: '150px',
                                  objectFit: 'contain',
                                  borderRadius: '4px',
                                  border: '1px solid #32324d'
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Right Column */}
                    <div>
                      <h4 style={{
                        fontSize: '16px',
                        fontWeight: 'bold',
                        color: 'white',
                        marginBottom: '12px',
                        margin: '0 0 12px 0'
                      }}>
                        Metadata
                      </h4>

                      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Vendor:</span>
                          <span style={{ color: 'white', marginLeft: '8px' }}>{banner.vendor?.name || 'No vendor'}</span>
                        </div>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Status:</span>
                          <span style={{ marginLeft: '8px' }}>
                            <StatusBadge status={banner.publishedAt ? 'published' : 'draft'} />
                          </span>
                        </div>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Created:</span>
                          <span style={{ color: 'white', marginLeft: '8px' }}>
                            {new Date(banner.createdAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                        <div>
                          <span style={{ color: '#a5a5ba', fontSize: '12px' }}>Updated:</span>
                          <span style={{ color: 'white', marginLeft: '8px' }}>
                            {new Date(banner.updatedAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>

                        <div style={{ marginTop: '16px', display: 'flex', gap: '8px' }}>
                          <button
                            onClick={() => handleEdit(banner.documentId)}
                            style={{
                              padding: '8px 16px',
                              backgroundColor: '#4945ff',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '14px',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#3832e1';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#4945ff';
                            }}
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            Edit Banner
                          </button>

                          <button
                            onClick={() => handleDelete(banner.documentId)}
                            style={{
                              padding: '8px 16px',
                              backgroundColor: 'transparent',
                              color: '#ff5252',
                              border: '1px solid #32324d',
                              borderRadius: '4px',
                              fontSize: '14px',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#d32f2f';
                              e.currentTarget.style.color = 'white';
                              e.currentTarget.style.border = '1px solid #d32f2f';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#ff5252';
                              e.currentTarget.style.border = '1px solid #32324d';
                            }}
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M10 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M14 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            Delete Banner
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        ) : (
          <div style={{
            padding: '40px',
            textAlign: 'center',
            color: '#a5a5ba'
          }}>
            No banners found
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 0 && (
        <div style={{ marginTop: '16px' }}>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
          />
        </div>
      )}



      {/* Add Banner Modal */}
      <AddBannerModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddBanner}
      />

      {/* Edit Banner Modal */}
      <EditBannerModal
        isOpen={showEditModal}
        banner={editingBanner}
        onClose={() => {
          setShowEditModal(false);
          setEditingBanner(null);
        }}
        onSubmit={handleEditBanner}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ ...deleteModal, isOpen: false })}
        onConfirm={confirmDelete}
        title={deleteModal.isBulk ? 'Confirm Bulk Delete' : 'Confirm Delete'}
        message={deleteModal.isBulk
          ? `Are you sure you want to delete ${deleteModal.itemName}? This action cannot be undone.`
          : 'Are you sure you want to delete this banner? This action cannot be undone.'
        }
        itemName={deleteModal.isBulk ? undefined : deleteModal.itemName}
      />

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default BannerDataTable;
