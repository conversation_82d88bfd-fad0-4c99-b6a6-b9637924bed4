import { useState, useRef, useEffect } from 'react';
import SimpleMediaPicker from '../ui/shared/media/SimpleMediaPicker';
import VendorDropdown from '../ui/shared/VendorDropdown';
import { Banner, BannerUpdateData } from '../../services/bannerService';
import { mediaService } from '../../services/mediaService';

interface EditBannerModalProps {
  isOpen: boolean;
  banner: Banner | null;
  onClose: () => void;
  onSubmit: (data: BannerUpdateData) => void;
}

const EditBannerModal = ({ isOpen, banner, onClose, onSubmit }: EditBannerModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<BannerUpdateData>({
    title: '',
    media: '',
    vendor: '',
    publishedAt: null
  });

  // Update form data when banner changes
  useEffect(() => {
    if (banner) {
      setFormData({
        title: banner.title || '',
        media: banner.media?.documentId || '',
        vendor: banner.vendor?.documentId || '',
        publishedAt: banner.publishedAt
      });
    }
  }, [banner]);

  // Current media value is the documentId; SimpleMediaPicker will resolve preview
  // when given a documentId.
  // Preview is handled internally by SimpleMediaPicker via mediaService.findOne.

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVendorChange = (vendor: string) => {
    setFormData(prev => ({
      ...prev,
      vendor
    }));
  };

  const handleImageSelect = (imageUrl: string) => {
    setFormData(prev => ({
      ...prev,
      media: imageUrl
    }));
  };

  const handleStatusToggle = () => {
    setFormData(prev => ({
      ...prev,
      publishedAt: prev.publishedAt ? null : new Date().toISOString()
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log('Updating banner with data:', formData);
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error updating banner:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (banner) {
      setFormData({
        title: banner.title || '',
        media: banner.media?.documentId || '',
        vendor: banner.vendor?.documentId || '',
        publishedAt: banner.publishedAt
      });
    }
    onClose();
  };

  if (!isOpen || !banner) return null;

  const isPublished = !!formData.publishedAt;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div
        style={{
          backgroundColor: '#212134',
          borderRadius: '8px',
          border: '1px solid #32324d',
          width: '100%',
          maxWidth: '600px',
          maxHeight: '90vh',
          overflow: 'auto',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)'
        }}
      >
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '24px 32px',
          borderBottom: '1px solid #32324d'
        }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            margin: 0
          }}>
            Edit Banner
          </h2>

          <button
            onClick={handleCancel}
            style={{
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'transparent',
              border: '1px solid #32324d',
              borderRadius: '4px',
              color: '#a5a5ba',
              cursor: 'pointer'
            }}
            title="Close"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '32px' }}>
            <div style={{
              display: 'grid',
              gap: '24px'
            }}>
              {/* Image Picker - First */}
              <div>
                <SimpleMediaPicker
                  label="Image"
                  value={formData.media || ''}
                  onChange={handleImageSelect}
                />
              </div>

              {/* Vendor - Second */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  marginBottom: '8px',
                  color: 'white'
                }}>
                  Vendor
                </label>
                <VendorDropdown
                  value={formData.vendor || ''}
                  onChange={handleVendorChange}
                  showAllOption={false}
                />
              </div>

              {/* Title - Third */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  marginBottom: '8px',
                  color: 'white'
                }}>
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title || ''}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    backgroundColor: '#181826',
                    color: 'white',
                    border: '1px solid #32324d',
                    borderRadius: '6px',
                    fontSize: '16px',
                    outline: 'none'
                  }}
                  placeholder="Enter banner title"
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '24px 32px',
            borderTop: '1px solid #32324d'
          }}>
            {/* Published Switch - Left side */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                cursor: 'pointer',
                color: 'white',
                fontSize: '16px'
              }}>
                <button
                  type="button"
                  onClick={handleStatusToggle}
                  style={{
                    width: '48px',
                    height: '24px',
                    borderRadius: '12px',
                    border: 'none',
                    backgroundColor: isPublished ? '#4945ff' : '#32324d',
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease'
                  }}
                >
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    backgroundColor: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: isPublished ? '26px' : '2px',
                    transition: 'left 0.2s ease'
                  }} />
                </button>
                <span>Published</span>
              </label>
            </div>

            {/* Buttons - Right side */}
            <div style={{
              display: 'flex',
              gap: '16px'
            }}>
              <button
                type="button"
                onClick={handleCancel}
                disabled={isSubmitting}
                style={{
                  padding: '12px 24px',
                  backgroundColor: 'transparent',
                  color: '#a5a5ba',
                  border: '1px solid #32324d',
                  borderRadius: '6px',
                  fontSize: '16px',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  opacity: isSubmitting ? 0.6 : 1
                }}
              >
                Cancel
              </button>

              <button
                type="submit"
                disabled={isSubmitting}
                style={{
                  padding: '12px 24px',
                  backgroundColor: isSubmitting ? '#32324d' : '#4945ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '16px',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {isSubmitting && (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid transparent',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                )}
                {isSubmitting ? 'Updating...' : 'Update Banner'}
              </button>
            </div>
          </div>
        </form>
      </div>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default EditBannerModal;
