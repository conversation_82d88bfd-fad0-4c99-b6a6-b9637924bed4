import { useRef } from 'react';

interface DatePickerProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

const DatePicker = ({ label, name, value, onChange, placeholder }: DatePickerProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    inputRef.current?.showPicker?.();
  };

  const formatDisplayDate = (dateValue: string) => {
    if (!dateValue) return '';
    const date = new Date(dateValue);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div>
      <label style={{
        display: 'block',
        fontSize: '12px',
        fontWeight: 'bold',
        marginBottom: '4px',
        color: 'white'
      }}>
        {label}
      </label>
      
      <div
        onClick={handleClick}
        style={{
          width: '100%',
          padding: '8px 12px',
          backgroundColor: '#212134',
          color: 'white',
          border: '1px solid #32324d',
          borderRadius: '4px',
          fontSize: '14px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: '40px',
          position: 'relative'
        }}
      >
        <span style={{
          color: value ? 'white' : '#a5a5ba',
          fontSize: '14px'
        }}>
          {value ? formatDisplayDate(value) : (placeholder || 'Select date')}
        </span>
        
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none"
          style={{ color: '#a5a5ba' }}
        >
          <path 
            d="M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
        </svg>

        <input
          ref={inputRef}
          type="date"
          name={name}
          value={value}
          onChange={onChange}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            opacity: 0,
            cursor: 'pointer'
          }}
        />
      </div>
    </div>
  );
};

export default DatePicker;