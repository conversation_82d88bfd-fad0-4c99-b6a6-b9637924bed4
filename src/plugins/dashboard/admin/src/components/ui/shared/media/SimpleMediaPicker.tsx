import React, { useState, useRef, useEffect } from 'react';
import { mediaService, MediaFile } from '../../../../services/mediaService';

interface SimpleMediaPickerProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

const SimpleMediaPicker = ({ value, onChange, label }: SimpleMediaPickerProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [selectedFileName, setSelectedFileName] = useState('');
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load media files from API
  const loadMediaFiles = async (page = 1, search = '') => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.findMany({
        page,
        pageSize: 12,
        search,
        mime: 'image' // Only load images
      });
      setMediaFiles(response.data);
      setTotalPages(response.meta.pagination.pageCount);
      setCurrentPage(page);
    } catch (err) {
      console.error('Error loading media files:', err);
      setError('Failed to load media files');
    } finally {
      setLoading(false);
    }
  };

  // Sync preview with value prop
  useEffect(() => {
    if (value) {
      // If we have a value, load the media info to show preview
      const loadMediaInfo = async () => {
        try {
          console.log('Loading media info for documentId:', value);
          const response = await mediaService.findOne(value);
          console.log('Media info response:', response);
          if (response.data) {
            setPreviewUrl(response.data.url);
            setSelectedFileName(response.data.name);
          }
        } catch (err) {
          console.error('Error loading media info for documentId:', value, err);
        }
      };
      loadMediaInfo();
    } else {
      setPreviewUrl('');
      setSelectedFileName('');
    }
  }, [value]);

  // Load media files when modal opens
  useEffect(() => {
    if (isModalOpen) {
      loadMediaFiles(1, searchTerm);
    }
  }, [isModalOpen]);

  // Handle search with debounce
  useEffect(() => {
    if (!isModalOpen) return;

    const timeoutId = setTimeout(() => {
      loadMediaFiles(1, searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, isModalOpen]);

  const handleImageSelect = (media: MediaFile) => {
    setPreviewUrl(media.url);
    setSelectedFileName(media.name);
    onChange(media.documentId); // Pass documentId instead of URL
    setIsModalOpen(false);
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        setUploading(true);
        setError(null);
        console.log('Uploading file:', file.name);

        // Show immediate preview
        const tempUrl = URL.createObjectURL(file);
        setPreviewUrl(tempUrl);
        setSelectedFileName(file.name);

        const response = await mediaService.upload(file);
        console.log('Upload response:', response);

        if (response.data && response.data.length > 0) {
          const uploadedFile = response.data[0];
          setPreviewUrl(uploadedFile.url);
          setSelectedFileName(uploadedFile.name);
          onChange(uploadedFile.documentId);

          // Clean up temp URL
          URL.revokeObjectURL(tempUrl);

          // Refresh media library if modal is open
          if (isModalOpen) {
            loadMediaFiles(currentPage, searchTerm);
          }
        }
      } catch (err) {
        console.error('Error uploading file:', err);
        setError('Failed to upload file');
        // Reset preview on error
        setPreviewUrl('');
        setSelectedFileName('');
      } finally {
        setUploading(false);
      }
    }
  };

  const handleRemove = () => {
    setPreviewUrl('');
    setSelectedFileName('');
    onChange('');
  };

  return (
    <div>
      {label && (
        <div style={{ marginBottom: '12px' }}>
          <label style={{
            display: 'block',
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: 'white'
          }}>
            {label}
          </label>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'normal',
            color: '#a5a5ba'
          }}>
            Media
          </label>
        </div>
      )}

      {/* Media Preview */}
      <div style={{
        width: '100%',
        height: '120px',
        borderRadius: '8px',
        backgroundColor: '#181826',
        border: '2px dashed #32324d',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        marginBottom: '12px'
      }}>
        {previewUrl ? (
          <>
            <img
              src={previewUrl}
              alt="Preview"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain'
              }}
            />
            <button
              type="button"
              onClick={handleRemove}
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                width: '28px',
                height: '28px',
                borderRadius: '50%',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                border: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ✕
            </button>
          </>
        ) : (
          <div style={{
            textAlign: 'center',
            color: '#a5a5ba'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '2px' }}>📷</div>
            <div style={{ fontSize: '14px', marginBottom: '12px' }}>
              {selectedFileName ? `Selected: ${selectedFileName}` : 'No media selected'}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{
        display: 'flex',
        gap: '8px'
      }}>
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading}
          style={{
            flex: 1,
            padding: '10px 16px',
            backgroundColor: uploading ? '#32324d' : '#4945ff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: uploading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 15V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V15" stroke="currentColor" strokeWidth="2"/>
            <path d="M17 8L12 3L7 8" stroke="currentColor" strokeWidth="2"/>
            <path d="M12 3V15" stroke="currentColor" strokeWidth="2"/>
          </svg>
          {uploading ? 'Uploading...' : 'Upload File'}
        </button>

        <button
          type="button"
          onClick={() => setIsModalOpen(true)}
          style={{
            flex: 1,
            padding: '10px 16px',
            backgroundColor: 'transparent',
            color: '#a5a5ba',
            border: '1px solid #32324d',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2"/>
          </svg>
          Browse Files
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div style={{
          marginTop: '8px',
          padding: '8px 12px',
          backgroundColor: '#2d1b1b',
          border: '1px solid #ff5252',
          borderRadius: '4px',
          color: '#ff5252',
          fontSize: '14px'
        }}>
          {error}
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        style={{ display: 'none' }}
      />

      {/* Media Library Modal */}
      {isModalOpen && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }}>
          <div style={{
            backgroundColor: '#212134',
            borderRadius: '8px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '80vh',
            border: '1px solid #32324d',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '16px 24px',
              borderBottom: '1px solid #32324d',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: 'bold',
                color: 'white',
                margin: 0
              }}>
                Select Media
              </h3>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: '#a5a5ba',
                  fontSize: '20px',
                  cursor: 'pointer',
                  width: '32px',
                  height: '32px',
                  borderRadius: '4px'
                }}
              >
                ✕
              </button>
            </div>

            {/* Search and Upload */}
            <div style={{
              padding: '16px 24px',
              borderBottom: '1px solid #32324d',
              display: 'flex',
              gap: '12px',
              alignItems: 'center'
            }}>
              <input
                type="text"
                placeholder="Search media..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  flex: 1,
                  padding: '8px 12px',
                  backgroundColor: '#181826',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  outline: 'none'
                }}
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
                style={{
                  padding: '8px 16px',
                  backgroundColor: uploading ? '#32324d' : '#4945ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '14px',
                  cursor: uploading ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {uploading ? 'Uploading...' : '+ Upload'}
              </button>
            </div>

            {/* Media Grid */}
            <div style={{
              flex: 1,
              padding: '24px',
              overflowY: 'auto'
            }}>
              {loading ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  padding: '40px',
                  color: '#a5a5ba'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    border: '3px solid #32324d',
                    borderTop: '3px solid #4945ff',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                  <span style={{ marginLeft: '12px' }}>Loading media...</span>
                </div>
              ) : error ? (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  color: '#ff5252'
                }}>
                  <p>{error}</p>
                  <button
                    onClick={() => loadMediaFiles(currentPage, searchTerm)}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#4945ff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      marginTop: '12px'
                    }}
                  >
                    Retry
                  </button>
                </div>
              ) : mediaFiles.length > 0 ? (
                <>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
                    gap: '16px',
                    marginBottom: '24px'
                  }}>
                    {mediaFiles.map((media) => (
                      <div
                        key={media.documentId}
                        onClick={() => handleImageSelect(media)}
                        style={{
                          aspectRatio: '16/9',
                          borderRadius: '8px',
                          overflow: 'hidden',
                          cursor: 'pointer',
                          border: '2px solid transparent',
                          transition: 'border-color 0.2s',
                          position: 'relative'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#4945ff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = 'transparent';
                        }}
                      >
                        <img
                          src={media.url}
                          alt={media.alternativeText || media.name}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                        />
                        <div style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                          color: 'white',
                          padding: '8px',
                          fontSize: '12px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {media.name}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: '8px',
                      marginTop: '16px'
                    }}>
                      <button
                        onClick={() => loadMediaFiles(currentPage - 1, searchTerm)}
                        disabled={currentPage <= 1}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: currentPage <= 1 ? '#32324d' : '#4945ff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: currentPage <= 1 ? 'not-allowed' : 'pointer',
                          fontSize: '14px'
                        }}
                      >
                        Previous
                      </button>
                      <span style={{ color: '#a5a5ba', fontSize: '14px' }}>
                        Page {currentPage} of {totalPages}
                      </span>
                      <button
                        onClick={() => loadMediaFiles(currentPage + 1, searchTerm)}
                        disabled={currentPage >= totalPages}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: currentPage >= totalPages ? '#32324d' : '#4945ff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer',
                          fontSize: '14px'
                        }}
                      >
                        Next
                      </button>
                    </div>
                  )}
                </>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  color: '#a5a5ba'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📷</div>
                  <p>No media files found</p>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#4945ff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      marginTop: '12px'
                    }}
                  >
                    Upload First Image
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default SimpleMediaPicker;
