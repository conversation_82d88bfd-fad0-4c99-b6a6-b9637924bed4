// Using fetch instead of @strapi/helper-plugin for compatibility
const request = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export interface Banner {
  id: string;
  documentId: string;
  title: string;
  media?: {
    id: string;
    documentId: string;
    name: string;
    url: string;
    mime: string;
    size: number;
  };
  vendor?: {
    id: string;
    documentId: string;
    name: string;
  };
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface BannerCreateData {
  title: string;
  media?: string; // documentId of media
  vendor?: string; // documentId of vendor
  publishedAt?: string | null;
}

export interface BannerUpdateData extends Partial<BannerCreateData> {}

export interface BannerListResponse {
  data: Banner[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface BannerFilters {
  search?: string;
  vendor?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface BannerListParams extends BannerFilters {
  page?: number;
  pageSize?: number;
  sort?: string;
}

class BannerService {
  private baseUrl = '/api/banners';

  async findMany(params: BannerListParams = {}): Promise<BannerListResponse> {
    const queryParams = new URLSearchParams();

    // Pagination
    if (params.page) queryParams.append('pagination[page]', params.page.toString());
    if (params.pageSize) queryParams.append('pagination[pageSize]', params.pageSize.toString());

    // Sorting
    if (params.sort) queryParams.append('sort', params.sort);

    // Populate relations
    queryParams.append('populate[media]', 'true');
    queryParams.append('populate[vendor]', 'true');

    // Filters
    if (params.search) {
      queryParams.append('filters[$or][0][title][$containsi]', params.search);
    }

    if (params.vendor && params.vendor !== 'all') {
      queryParams.append('filters[vendor][documentId][$eq]', params.vendor);
    }

    if (params.status && params.status.trim() !== '') {
      if (params.status === 'published') {
        queryParams.append('filters[publishedAt][$notNull]', 'true');
      } else if (params.status === 'draft') {
        queryParams.append('filters[publishedAt][$null]', 'true');
      }
    }

    if (params.dateFrom) {
      queryParams.append('filters[createdAt][$gte]', params.dateFrom);
    }

    if (params.dateTo) {
      queryParams.append('filters[createdAt][$lte]', params.dateTo);
    }

    const url = `${this.baseUrl}?${queryParams.toString()}`;
    return request(url, { method: 'GET' });
  }

  async findOne(documentId: string): Promise<{ data: Banner }> {
    const queryParams = new URLSearchParams();
    queryParams.append('populate[media]', 'true');
    queryParams.append('populate[vendor]', 'true');

    const url = `${this.baseUrl}/${documentId}?${queryParams.toString()}`;
    return request(url, { method: 'GET' });
  }

  async create(data: BannerCreateData): Promise<{ data: Banner }> {
    console.log('BannerService.create called with data:', data);
    try {
      const response = await request(this.baseUrl, {
        method: 'POST',
        body: JSON.stringify({ data })
      });
      console.log('BannerService.create response:', response);
      return response;
    } catch (error) {
      console.error('BannerService.create error:', error);
      throw error;
    }
  }

  async update(documentId: string, data: BannerUpdateData): Promise<{ data: Banner }> {
    console.log('BannerService.update called with documentId:', documentId, 'data:', data);
    try {
      const response = await request(`${this.baseUrl}/${documentId}`, {
        method: 'PUT',
        body: JSON.stringify({ data })
      });
      console.log('BannerService.update response:', response);
      return response;
    } catch (error) {
      console.error('BannerService.update error:', error);
      throw error;
    }
  }

  async delete(documentId: string): Promise<{ data: Banner }> {
    return request(`${this.baseUrl}/${documentId}`, {
      method: 'DELETE'
    });
  }

  async bulkDelete(documentIds: string[]): Promise<{ data: Banner[] }> {
    // Since Strapi doesn't have built-in bulk delete, we'll delete one by one
    const deletePromises = documentIds.map(id => this.delete(id));
    const results = await Promise.allSettled(deletePromises);

    const successful = results
      .filter((result): result is PromiseFulfilledResult<{ data: Banner }> =>
        result.status === 'fulfilled'
      )
      .map(result => result.value.data);

    const failed = results
      .filter((result): result is PromiseRejectedResult =>
        result.status === 'rejected'
      );

    if (failed.length > 0) {
      console.warn(`Failed to delete ${failed.length} banners:`, failed);
    }

    return { data: successful };
  }

  async publish(documentId: string): Promise<{ data: Banner }> {
    return this.update(documentId, { publishedAt: new Date().toISOString() });
  }

  async unpublish(documentId: string): Promise<{ data: Banner }> {
    return this.update(documentId, { publishedAt: null });
  }

  async bulkPublish(documentIds: string[]): Promise<{ data: Banner[] }> {
    const publishPromises = documentIds.map(id => this.publish(id));
    const results = await Promise.allSettled(publishPromises);

    const successful = results
      .filter((result): result is PromiseFulfilledResult<{ data: Banner }> =>
        result.status === 'fulfilled'
      )
      .map(result => result.value.data);

    return { data: successful };
  }

  async bulkUnpublish(documentIds: string[]): Promise<{ data: Banner[] }> {
    const unpublishPromises = documentIds.map(id => this.unpublish(id));
    const results = await Promise.allSettled(unpublishPromises);

    const successful = results
      .filter((result): result is PromiseFulfilledResult<{ data: Banner }> =>
        result.status === 'fulfilled'
      )
      .map(result => result.value.data);

    return { data: successful };
  }
}

export const bannerService = new BannerService();
export default bannerService;
