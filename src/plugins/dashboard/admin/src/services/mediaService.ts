// Using fetch for API requests
const request = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export interface MediaFile {
  id: string;
  documentId: string;
  name: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: any;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface MediaListResponse {
  data: MediaFile[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

class MediaService {
  private baseUrl = '/api/upload/files';

  async findMany(params: {
    page?: number;
    pageSize?: number;
    search?: string;
    mime?: string; // Filter by mime type (e.g., 'image')
  } = {}): Promise<MediaListResponse> {
    const queryParams = new URLSearchParams();

    // Pagination
    if (params.page) queryParams.append('pagination[page]', params.page.toString());
    if (params.pageSize) queryParams.append('pagination[pageSize]', params.pageSize.toString());

    // Search by name
    if (params.search) {
      queryParams.append('filters[name][$containsi]', params.search);
    }

    // Filter by mime type (e.g., only images)
    if (params.mime) {
      queryParams.append('filters[mime][$startsWith]', params.mime);
    }

    const url = `${this.baseUrl}?${queryParams.toString()}`;
    return request(url, { method: 'GET' });
  }

  async findOne(documentId: string): Promise<{ data: MediaFile }> {
    // For Strapi v5, we need to query by documentId using filters
    const queryParams = new URLSearchParams();
    queryParams.append('filters[documentId][$eq]', documentId);

    const url = `${this.baseUrl}?${queryParams.toString()}`;
    const response = await request(url, { method: 'GET' });

    // The response will be an array, so we need to extract the first item
    if (response.data && response.data.length > 0) {
      return { data: response.data[0] };
    } else {
      throw new Error(`Media file with documentId ${documentId} not found`);
    }
  }

  async upload(file: File): Promise<{ data: MediaFile[] }> {
    const formData = new FormData();
    formData.append('files', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async delete(documentId: string): Promise<{ data: MediaFile }> {
    return request(`${this.baseUrl}/${documentId}`, {
      method: 'DELETE'
    });
  }
}

export const mediaService = new MediaService();
export default mediaService;
