// Using fetch for API requests
const request = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export interface Vendor {
  id: string;
  documentId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface VendorListResponse {
  data: Vendor[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

class VendorService {
  private baseUrl = '/api/vendors';

  async findMany(params: { page?: number; pageSize?: number; search?: string } = {}): Promise<VendorListResponse> {
    const queryParams = new URLSearchParams();
    
    // Pagination
    if (params.page) queryParams.append('pagination[page]', params.page.toString());
    if (params.pageSize) queryParams.append('pagination[pageSize]', params.pageSize.toString());
    
    // Search
    if (params.search) {
      queryParams.append('filters[name][$containsi]', params.search);
    }

    const url = `${this.baseUrl}?${queryParams.toString()}`;
    return request(url, { method: 'GET' });
  }

  async findOne(documentId: string): Promise<{ data: Vendor }> {
    const url = `${this.baseUrl}/${documentId}`;
    return request(url, { method: 'GET' });
  }
}

export const vendorService = new VendorService();
export default vendorService;
